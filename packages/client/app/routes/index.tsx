import { useEffect } from "react";
import { useNavigate } from "react-router";
import type { Route } from "./+types/index";
import { useAuth } from "../features/auth/AuthContext";
import {
  Text,
  But<PERSON>,
  Card,
  CardHeader,
  makeStyles,
  tokens,
  CardFooter,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    minHeight: "100vh",
    padding: tokens.spacingHorizontalL,
    backgroundColor: tokens.colorNeutralBackground2,
  },
  card: {
    maxWidth: "600px",
    width: "100%",
  },
  content: {
    padding: tokens.spacingHorizontalL,
    textAlign: "center",
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  header: {
    marginBottom: tokens.spacingVerticalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Vast" },
    { name: "description", content: "Welcome to Vast" },
  ];
}

export default function Home() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { isAuthenticated, user, logout, isLoading } = useAuth();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login", { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleLogout = () => {
    logout();
    navigate("/login", { replace: true });
  };

  // Show loading or redirect
  if (isLoading || !isAuthenticated) {
    return null;
  }

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <CardHeader>
          <div className={styles.header}>
            <Text size={800} weight="semibold">
              Welcome to Vast
            </Text>
          </div>
        </CardHeader>
        <div className={styles.content}>
          <Text size={400}>
            Hello, {user?.name}! You are successfully logged in.
          </Text>
          <Text size={300}>Email: {user?.email}</Text>
          <CardFooter>
            <Button
              appearance="secondary"
              onClick={handleLogout}
              style={{ marginTop: tokens.spacingVerticalL }}
            >
              Sign Out
            </Button>
          </CardFooter>
        </div>
      </Card>
    </div>
  );
}
