import React from 'react';
import type { Route } from './+types/requirements';
import {
  Text,
  Card,
  CardHeader,
  CardPreview,
  makeStyles,
  tokens,
} from '@fluentui/react-components';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalL,
    maxWidth: '1200px',
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: '100%',
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Requirements - Vast' },
    { name: 'description', content: 'Manage your project requirements' },
  ];
}

export default function Requirements() {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text size={800} weight="semibold">
          Requirements
        </Text>
        <Text size={400} style={{ marginTop: tokens.spacingVerticalXS }}>
          Define and manage your project requirements
        </Text>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Getting Started
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              This is where you'll manage your project requirements. You can create, 
              edit, and organize requirements to ensure your project meets all necessary 
              specifications and stakeholder needs.
            </Text>
          </div>
        </CardPreview>
      </Card>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Recent Requirements
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              No requirements have been created yet. Start by adding your first requirement 
              to begin organizing your project specifications.
            </Text>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
}
