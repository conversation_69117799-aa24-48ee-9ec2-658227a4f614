import {
  type RouteConfig,
  index,
  route,
  layout,
} from "@react-router/dev/routes";

export default [
  // Auth routes (no layout)
  route("login", "routes/login.tsx"),
  route("register", "routes/register.tsx"),

  // Main app routes (with layout)
  layout("components/layout/AppLayout.tsx", [
    index("routes/requirements.tsx"), // Default to requirements page
    route("requirements", "routes/requirements.tsx"),
    route("initiatives", "routes/initiatives.tsx"),
    route("personas", "routes/personas.tsx"),
  ]),
] satisfies RouteConfig;
