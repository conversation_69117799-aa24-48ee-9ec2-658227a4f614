import React from 'react';
import { useLocation, useNavigate } from 'react-router';
import {
  TabList,
  Tab,
  makeStyles,
  tokens,
} from '@fluentui/react-components';
import {
  DocumentRegular,
  DocumentFilled,
  TargetRegular,
  TargetFilled,
  PersonRegular,
  PersonFilled,
  bundleIcon,
} from '@fluentui/react-icons';

const DocumentIcon = bundleIcon(DocumentFilled, DocumentRegular);
const TargetIcon = bundleIcon(TargetFilled, TargetRegular);
const PersonIcon = bundleIcon(PersonFilled, PersonRegular);

const useStyles = makeStyles({
  sidebar: {
    width: '240px',
    height: '100%',
    backgroundColor: tokens.colorNeutralBackground2,
    borderRight: `1px solid ${tokens.colorNeutralStroke2}`,
    display: 'flex',
    flexDirection: 'column',
  },
  navigation: {
    padding: tokens.spacingVerticalM,
    flex: 1,
  },
  tabList: {
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  tab: {
    justifyContent: 'flex-start',
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalM}`,
    marginBottom: tokens.spacingVerticalXS,
    borderRadius: tokens.borderRadiusMedium,
    minHeight: '40px',
    width: '100%',
  },
});

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'requirements',
    label: 'Requirements',
    path: '/requirements',
    icon: DocumentIcon,
  },
  {
    id: 'initiatives',
    label: 'Initiatives',
    path: '/initiatives',
    icon: TargetIcon,
  },
  {
    id: 'personas',
    label: 'Personas',
    path: '/personas',
    icon: PersonIcon,
  },
];

export function Sidebar() {
  const styles = useStyles();
  const location = useLocation();
  const navigate = useNavigate();

  // Determine the active tab based on current path
  const getActiveTab = () => {
    const currentPath = location.pathname;
    const activeItem = navigationItems.find(item => 
      currentPath.startsWith(item.path)
    );
    return activeItem?.id || 'requirements'; // Default to requirements
  };

  const handleTabSelect = (event: any, data: any) => {
    const selectedItem = navigationItems.find(item => item.id === data.value);
    if (selectedItem) {
      navigate(selectedItem.path);
    }
  };

  return (
    <aside className={styles.sidebar}>
      <nav className={styles.navigation}>
        <TabList
          selectedValue={getActiveTab()}
          onTabSelect={handleTabSelect}
          vertical
          className={styles.tabList}
        >
          {navigationItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <Tab
                key={item.id}
                value={item.id}
                className={styles.tab}
                icon={<IconComponent />}
              >
                {item.label}
              </Tab>
            );
          })}
        </TabList>
      </nav>
    </aside>
  );
}
