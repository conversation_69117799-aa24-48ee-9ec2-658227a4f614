import { useEffect } from "react";
import { useNavigate, Outlet } from "react-router";
import { makeStyles, tokens } from "@fluentui/react-components";
import { useAuth } from "../../features/auth/AuthContext";
import { Header } from "./Header";
import { Sidebar } from "./Sidebar";

const useStyles = makeStyles({
  layout: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    overflow: "hidden",
  },
  main: {
    display: "flex",
    flex: 1,
    overflow: "hidden",
  },
  content: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
  },
  contentArea: {
    flex: 1,
    padding: tokens.spacingHorizontalL,
    backgroundColor: tokens.colorNeutralBackground1,
    overflow: "auto",
  },
});

export function AppLayout() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login", { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading or redirect
  if (isLoading || !isAuthenticated) {
    return null;
  }

  console.log("AppLayout rendered");

  return (
    <div className={styles.layout}>
      <Header />
      <div className={styles.main}>
        <Sidebar />
        <div className={styles.content}>
          <main className={styles.contentArea}>
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
}
