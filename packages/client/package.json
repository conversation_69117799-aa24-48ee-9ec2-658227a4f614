{"name": "client", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@fluentui/react-components": "^9.66.2", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "isbot": "^5.1.27", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.5.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-cjs-interop": "^2.2.0", "vite-tsconfig-paths": "^5.1.4"}}